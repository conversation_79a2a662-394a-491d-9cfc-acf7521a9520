package reimbursement

import (
	api "marketing/internal/api/reimbursement"
	dao1 "marketing/internal/dao"
	"marketing/internal/dao/admin_user"
	dao "marketing/internal/dao/reimbursement"
	appErr "marketing/internal/pkg/errors"

	"github.com/gin-gonic/gin"
)

type HomePageService interface {
	GetHomePage(c *gin.Context, req *api.HomePageReq) (*api.HomePageResp, error)
	GetClientSummary(c *gin.Context, req *api.ClientSummaryReq) ([]*api.ClientSummaryResp, error)
	GetClientOrders(c *gin.Context, req *api.ClientOrdersReq) ([]*api.ClientOrdersResp, error)
	GetClientDetail(c *gin.Context, req *api.OrderReq) (*api.ReimbursementDetail, error)
	ApplyAdvertExpense(c *gin.Context, req *api.AdvertExpenseApplyReq) error
	ChangeAdvertExpense(c *gin.Context, req *api.AdvertExpenseApplyReq) (bool, error)
}

type homePageService struct {
	repo          dao.ReimbursementRepository
	userRepo      admin_user.UserDao
	balanceRepo   dao.BalanceRepository
	policyRepo    dao.PolicyRepository
	kingdeeAgency dao1.KingDeeAgencyDao
}

func NewHomePageService(repo dao.ReimbursementRepository, userRepo admin_user.UserDao, balanceRepo dao.BalanceRepository, policyRepo dao.PolicyRepository) HomePageService {
	return &homePageService{
		repo:          repo,
		userRepo:      userRepo,
		balanceRepo:   balanceRepo,
		policyRepo:    policyRepo,
		kingdeeAgency: dao1.NewKingDeeAgencyDao(),
	}
}

// GetHomePage 获取报销首页数据
func (s *homePageService) GetHomePage(c *gin.Context, req *api.HomePageReq) (*api.HomePageResp, error) {
	// 获取当前用户ID
	uid := c.GetUint("uid")

	if uid == 0 {
		return nil, appErr.NewErr("用户未登录")
	}

	// 获取用户代理信息
	userAgency, err := s.userRepo.UserAgency(c, uid)
	if err != nil {
		return nil, appErr.NewErr("权限不足")
	}
	if userAgency == nil {
		return nil, appErr.NewErr("用户代理不存在")
	}

	// 获取金额信息
	standardBalanceQuota, err := s.balanceRepo.GetBalanceStandard(c, 0, req.CompanyID, "standard_balance_quota")
	if err != nil {
		return nil, err
	}

	// 获取待处理的申请单提醒
	pendingPromotionalProducts, err := s.repo.GetPendingPromotionalProducts(c, req.CompanyID, userAgency.ID)
	if err != nil {
		return nil, err
	}

	pendingAdvertExpense, err := s.repo.GetPendingAdvertExpense(c, req.CompanyID, userAgency.ID)
	if err != nil {
		return nil, err
	}

	pendingReimbursementList, err := s.repo.GetPendingReimbursementList(c, req.CompanyID, userAgency.ID)
	if err != nil {
		return nil, err
	}

	// 合并所有待处理任务
	var pendingTasks []api.PendingTask
	pendingTasks = append(pendingTasks, pendingPromotionalProducts...)
	pendingTasks = append(pendingTasks, pendingReimbursementList...)
	pendingTasks = append(pendingTasks, pendingAdvertExpense...)

	resp := &api.HomePageResp{
		StandardBalanceQuota: standardBalanceQuota.Balance,
		EstimateAmount:       0,
		PendingTask:          pendingTasks,
	}

	return resp, nil
}

// GetClientSummary 获取客户端汇总数据
func (s *homePageService) GetClientSummary(c *gin.Context, req *api.ClientSummaryReq) ([]*api.ClientSummaryResp, error) {
	// 获取当前用户ID
	uid := c.GetUint("uid")

	if uid == 0 {
		return nil, appErr.NewErr("用户未登录")
	}

	// 获取用户代理信息
	userAgency, err := s.userRepo.UserAgency(c, uid)
	if err != nil {
		return nil, appErr.NewErr("权限不足")
	}
	if userAgency == nil {
		return nil, appErr.NewErr("非总代用户，无权操作")
	}

	// 调用DAO层获取汇总数据（DAO层会自动获取代理商信息）
	results, err := s.repo.GetClientSummary(c, req.CompanyID, userAgency.ID, "", req.Archive)
	if err != nil {
		return nil, appErr.NewErr("获取汇总数据失败: " + err.Error())
	}

	return results, nil
}

// GetClientOrders 获取客户端订单数据
func (s *homePageService) GetClientOrders(c *gin.Context, req *api.ClientOrdersReq) ([]*api.ClientOrdersResp, error) {
	// 获取当前用户ID
	uid := c.GetUint("uid")

	if uid == 0 {
		return nil, appErr.NewErr("用户未登录")
	}
	//获取核销政策
	policyInfo, err := s.policyRepo.GetPolicyByID(req.PolicyID)
	if err != nil {
		return nil, err
	}
	if policyInfo == nil {
		return nil, appErr.NewErr("未找到核销政策信息")
	}
	// 获取用户代理信息
	userAgency, err := s.userRepo.UserAgency(c, uid)
	if err != nil {
		return nil, appErr.NewErr("权限不足")
	}
	if userAgency == nil {
		return nil, appErr.NewErr("非总代用户，无权操作")
	}
	req.TopAgency = userAgency.ID
	// 根据不同状态获取不同类型的订单数据
	switch {
	case req.Status >= 1 && req.Status <= 3:
		if policyInfo.PolicyType == "promotional_products" {
			return s.repo.GetPromotionalProductsOrders(c, req)
		} else if policyInfo.PolicyType == "advert_expense" {
			return s.repo.GetAdvertExpenseOrders(c, req)
		}
	case req.Status == 4:
		if policyInfo.PolicyType == "promotional_products" {
			return s.repo.GetPromotionalProductsApplySummaryOrders(c, req)
		} else if policyInfo.PolicyType == "advert_expense" {
			return s.repo.GetAdvertExpenseApplySummaryOrders(c, req)
		}
	case req.Status == 5 || req.Status == 6:
		return s.repo.GetReimbursementOrders(c, req)
	}

	return nil, appErr.NewErr("无效的状态值")
}

func (s *homePageService) GetClientDetail(c *gin.Context, req *api.OrderReq) (*api.ReimbursementDetail, error) {

	// 获取当前用户ID
	uid := c.GetUint("uid")
	if uid == 0 {
		return nil, appErr.NewErr("用户未登录")
	}

	// 获取用户代理信息
	userAgency, err := s.userRepo.UserAgency(c, uid)
	if err != nil {
		return nil, appErr.NewErr("权限不足")
	}
	if userAgency == nil {
		return nil, appErr.NewErr("非总代用户，无权操作")
	}

	data, err := s.repo.ReimbursementClientDetail(c, req.ID, userAgency.ID)
	if err != nil {
		return nil, err
	}
	//查询summary
	summaries, err := s.repo.GetDetailSummaryInfo(c, req.ID)
	if err != nil {
		return nil, appErr.NewErr("获取汇总信息失败: " + err.Error())
	}
	for i, summary := range summaries {
		if data.PolicyType == "promotional_products" {
			// 获取促销品申请单的详细信息
			product, err := s.repo.GetReimbursementProducts(c, summary.ID)
			if err != nil {
				return nil, appErr.NewErr("获取促销品申请单失败: " + err.Error())
			}
			if product == nil {
				return nil, appErr.NewErr("未找到促销品申请单")
			}
			summaries[i].Products = product
		}
		//获取apply_info
		applyInfo, err := s.repo.GetReimbursementApplyInfo(c, summary.ID, data.PolicyType)
		if err != nil {
			return nil, appErr.NewErr("获取申请信息失败: " + err.Error())
		}
		summaries[i].ApplicationInfo = applyInfo
	}
	data.ReimbursementSummary = summaries

	return data, nil
}

func (s *homePageService) ApplyAdvertExpense(c *gin.Context, req *api.AdvertExpenseApplyReq) error {
	// 获取当前用户ID
	uid := c.GetUint("uid")
	if uid == 0 {
		return appErr.NewErr("用户未登录")
	}

	// 获取用户代理信息
	userAgency, err := s.userRepo.UserAgency(c, uid)
	if err != nil {
		return appErr.NewErr("权限不足")
	}
	if userAgency == nil {
		return appErr.NewErr("非总代用户，无权操作")
	}
	//获取核销政策
	policyInfo, err := s.policyRepo.GetPolicyByID(req.PolicyID)
	if err != nil {
		return err
	}
	if policyInfo == nil {
		return appErr.NewErr("此政策已过期, 无法申请核销")
	}
	if policyInfo.Archive == 1 {
		return appErr.NewErr("此政策已归档, 无法申请")
	}
	// 获取公司
	companyInfo, err := s.kingdeeAgency.GetKingDeeAgencyByID(c, req.CompanyID)
	if err != nil {
		return appErr.NewErr("获取公司信息失败: " + err.Error())
	}
	if companyInfo == nil {
		return appErr.NewErr("未找到此公司信息, 无法申请核销")
	}
	if companyInfo.TopAgency != userAgency.ID {
		return appErr.NewErr("此公司非此代理商名下, 无法申请核销")
	}
	req.Uid = uid
	req.Agency = userAgency.ID
	req.Code = companyInfo.Code
	req.ReimbursementType = policyInfo.ReimbursementType
	// 调用DAO层进行申请
	err = s.repo.ApplyAdvertExpense(c, req)
	if err != nil {
		return appErr.NewErr("申请广告费用失败: " + err.Error())
	}

	return nil
}

// ChangeAdvertExpense 广告费用申请单修改
func (s *homePageService) ChangeAdvertExpense(c *gin.Context, req *api.AdvertExpenseApplyReq) (bool, error) {
	// 获取当前用户ID
	uid := c.GetUint("uid")
	if uid == 0 {
		return false, appErr.NewErr("用户未登录")
	}

	// 获取用户代理信息
	userAgency, err := s.userRepo.UserAgency(c, uid)
	if err != nil {
		return false, appErr.NewErr("权限不足")
	}
	if userAgency == nil {
		return false, appErr.NewErr("权限不足")
	}

	// 获取申请单详情
	agencyMap := map[string]int{
		"top_agency":    int(userAgency.ID),
		"second_agency": 0, // 这里需要根据实际业务逻辑调整
	}
	order, err := s.repo.GetAdvertExpenseOrder(c, req.ID, agencyMap)
	if err != nil {
		return false, err
	}
	if order == nil {
		return false, appErr.NewErr("未找到记录，无法修改")
	}

	// 检查状态是否允许修改
	if order.Status != 0 {
		return false, appErr.NewErr("状态错误，无法修改")
	}

	// 获取关联的政策信息
	policyInfo, err := s.repo.GetPolicyByOrder(c, req.ID, 2) // 2表示广告费用类型
	if err != nil {
		return false, err
	}
	if policyInfo == nil {
		return false, appErr.NewErr("此政策已过期, 无法修改")
	}
	if policyInfo.Archive == 1 {
		return false, appErr.NewErr("此政策已归档, 无法修改")
	}

	// 验证金额
	if req.Amount <= 0 {
		return false, appErr.NewErr("金额要求大于0")
	}
	req.Uid = uid
	req.Agency = userAgency.ID
	req.Code = order.Code
	req.ReimbursementType = policyInfo.ReimbursementType

	// 调用DAO层进行修改
	return s.repo.ChangeAdvertExpense(c, req)
}
